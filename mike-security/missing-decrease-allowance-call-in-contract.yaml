rules:
  - id: missing-decrease-allowance-call-in-contract
    pattern-either:
      - patterns:
          - pattern: |
              contract $CONTRACT {
                ...
                function $FUNC(...) $MODIFIERS {
                  ...
                  $INSTANCE.increaseAllowance($SPENDER, $AMOUNT);
                  ...
                }
                ...
              }
          - pattern-not-inside: |
              contract $CONTRACT {
                ...
                function $ANY_FUNC(...) $ANY_MODIFIERS {
                  ...
                  $INSTANCE.decreaseAllowance($ANY_SPENDER, $ANY_AMOUNT);
                  ...
                }
                ...
              }
      - patterns:
          - pattern: |
              contract $CONTRACT {
                ...
                function $FUNC(...) $MODIFIERS {
                  ...
                  $INSTANCE.approve($SPENDER, $INSTANCE.allowance($OWNER, $SPENDER).add($AMOUNT));
                  ...
                }
                ...
              }
          - pattern-not-inside: |
              contract $CONTRACT {
                ...
                function $ANY_FUNC(...) $ANY_MODIFIERS {
                  ...
                  $INSTANCE.decreaseAllowance($ANY_SPENDER, $ANY_AMOUNT);
                  ...
                }
                ...
              }
    message: "The contract '$CONTRACT' increases allowance in function '$FUNC' but does not have any corresponding decreaseAllowance calls in any function. This may leave unnecessary allowance for spenders."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "After increasing allowance, there should be a mechanism to decrease it in some part of the contract to prevent potential misuse of remaining allowance."